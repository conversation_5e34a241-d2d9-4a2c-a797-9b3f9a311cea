const bmsxService = require('../../services/bmsx/bmsx.service');

/**
 * Discovery Request - Trigger discovery request for BACnet devices via IoT Core
 */
module.exports = {
  friendlyName: 'Discovery Request',
  description: 'Trigger discovery request for BACnet devices via IoT Core',

  inputs: {
    // _userMeta: {
    //   type: 'json',
    //   required: true,
    //   example: { id: 'userId', _role: 'role', _site: 'siteId' },
    //   description: 'User meta information added by default to authenticated routes'
    // },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where discovery should be triggered'
    },

    slaveControllerId: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    }
  },

  exits: {
    success: {
      responseType: "ok",
      statusCode: 200,
    },
    badRequest: {
      responseType: "badRequest",
      statusCode: 400,
    },
    notFound: {
      responseType: "notFound",
      statusCode: 404,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { siteId, slaveControllerId } = inputs;

    try {
      const result = await bmsxService.triggerDiscoveryRequest(siteId, slaveControllerId);

      return exits.success({
        message: result?.message,
        requestId: result?.requestId
      });

    } catch (error) {
      sails.log.error('[BMS > discovery-request] Error:', error);

      if (error.code === 'E_NOT_FOUND') {
        return exits.badRequest({ message: error.message });
      }

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to trigger discovery request',
        error: error.message
      });
    }
  }
};
